import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, IsPositive, Length } from 'class-validator';
import { AutoMap } from '@automapper/classes';

export class CreateTenantDto {
  @AutoMap(() => String)
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'The name of the tenant',
    example: 'Nigeria',
    minLength: 1,
    name: 'name',
  })
  name: string;

  @AutoMap(() => String)
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'The unique code for the tenant',
    example: 'NGA',
    name: 'code',
    uniqueItems: true,
  })
  code: string;

  @AutoMap()
  @IsNotEmpty()
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  @ApiProperty({
    description: 'The price factor for the tenant',
    example: 1.00,
    minimum: 0,
    name: 'priceFactor',
  })
  priceFactor: number;

  @AutoMap()
  @IsNotEmpty()
  @IsString()
  @Length(3, 3)
  @ApiProperty({
    description: 'The currency code for the tenant',
    example: 'NGN',
    name: 'currencyCode',
  })
  currencyCode: string;

  // @AutoMap()
  // @IsNotEmpty()
  // @IsString()
  // @Length(1, 3)
  // @ApiProperty({
  //   description: 'The currency symbol for the tenant',
  //   example: '₦',
  //   name: 'currencySymbol',
  // })
  // currencySymbol: string;
}
