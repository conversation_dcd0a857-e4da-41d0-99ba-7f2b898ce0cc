import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Item } from './entities/item.entity';
import { Product } from '@core/product/entities/product.entity';
import { ItemController } from './item.controller';
import { ItemMapperService } from './item.mapper.service';
import { ItemService } from './item.service';
import { ItemValidationService } from './item.validation.service';

@Module({
  controllers: [ItemController],
  providers: [ItemService, ItemMapperService, ItemValidationService],
  exports: [ItemService],
  imports: [TypeOrmModule.forFeature([Item, Product]), LoggerModule],
})
export class ItemModule {}
