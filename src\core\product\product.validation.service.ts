import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { I18nService } from 'nestjs-i18n';
import { LoggerService } from '@common/logger/logger.service';
import { ValidationStrategy } from '@common/validation.strategy';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { Product } from './entities/product.entity';

@Injectable()
export class ProductValidationService implements ValidationStrategy<Product> {
  constructor(
    private readonly logger: LoggerService,
    @InjectRepository(Product) private readonly productRepository: Repository<Product>,
    private readonly i18n: I18nService,
  ) {
    this.logger.setContext(ProductValidationService.name);
  }

  async validate(data: Product, action: DatabaseAction) {
    const existingProduct = await this.productRepository.findOneBy({ id: data.id });

    if (action === DatabaseAction.CREATE) {
      // Add any creation-specific validations here
    }

    if (action === DatabaseAction.UPDATE && !existingProduct) {
      throw new NotFoundException(this.i18n.t('errors.not_found', { args: { entity: 'Product' } }));
    }
  }
}
