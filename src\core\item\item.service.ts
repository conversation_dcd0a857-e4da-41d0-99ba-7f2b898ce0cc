import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository, InjectEntityManager } from '@nestjs/typeorm';
import { Repository, EntityManager, FindManyOptions, ILike } from 'typeorm';
import { I18nService } from 'nestjs-i18n';
import { paginate } from 'nestjs-typeorm-paginate';
import { LoggerService } from '@common/logger/logger.service';
import { EntityServiceStrategy } from '@common/entity.service.strategy';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { EntityStatus } from '@common/base.entity';
import { PaginationQueryParams } from '@common/types/index.type';
import { Item } from './entities/item.entity';
import { Product } from '@core/product/entities/product.entity';
import { ItemValidationService } from './item.validation.service';
import { CreateItemDto } from './dto/create-item.dto';
import { BaseRepository } from '@common/base.repository';

@Injectable()
export class ItemService implements EntityServiceStrategy<Item> {
  constructor(
    @InjectRepository(Item) private readonly itemRepository: Repository<Item>,
    @InjectRepository(Product) private readonly productRepository: BaseRepository<Product>,
    private readonly itemValidator: ItemValidationService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(ItemService.name);
  }

  async create(data: Item): Promise<Item> {
    await this.itemValidator.validate(data, DatabaseAction.CREATE);
    return await this.entityManager.save(data);
  }

  async createFromDto(createItemDto: CreateItemDto): Promise<Item> {
    // Find the product
    const product = await this.productRepository.findOneBy({ id: createItemDto.productId });
    if (!product) {
      throw new NotFoundException(this.i18n.t('errors.not_found', { args: { entity: 'Product' } }));
    }

    // Create the item
    const item = new Item();
    item.name = createItemDto.name;
    item.description = createItemDto.description;
    item.price = createItemDto.price;
    item.unit = createItemDto.unit;
    item.product = product;

    await this.itemValidator.validate(item, DatabaseAction.CREATE);
    return await this.entityManager.save(item);
  }

  async modify(id: number, data: Item): Promise<Item> {
    await this.itemValidator.validate(data, DatabaseAction.UPDATE);
    return await this.itemRepository.save(data);
  }

  async findByPk(id: number): Promise<Item | null> {
    return await this.itemRepository.findOneBy({ id });
  }

  async findOneItem(id: number): Promise<Item> {
    const item = await this.itemRepository.findOne({
      where: { id },
      relations: ['product'],
    });
    if (!item) {
      throw new NotFoundException(this.i18n.t('errors.not_found', { args: { entity: 'Item' } }));
    }
    return item;
  }

  async findAllItems(params: PaginationQueryParams, route: string) {
    const { page, limit, search, filter } = params;
    const where = {};

    if (search) {
      where['name'] = ILike(`%${search}%`);
    }

    if (filter) {
      where['status'] = filter;
    }

    const options = {
      page,
      limit,
      route,
    };

    const queryOptions: FindManyOptions<Item> = {
      where,
      order: { createdAt: 'desc' },
      relations: ['product'],
    };

    const { items, meta, links } = await paginate(this.itemRepository, options, queryOptions);

    return { items, meta, links };
  }

  async activate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const item = await this.findOneItem(id);
        item.status = EntityStatus.ACTIVE;
        await this.itemRepository.save(item);
      }),
    );
  }

  async deactivate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const item = await this.findOneItem(id);
        item.status = EntityStatus.INACTIVE;
        await this.itemRepository.save(item);
      }),
    );
  }

  async remove(id: number): Promise<void> {
    const item = await this.findOneItem(id);
    await this.itemRepository.remove(item);
  }
}
