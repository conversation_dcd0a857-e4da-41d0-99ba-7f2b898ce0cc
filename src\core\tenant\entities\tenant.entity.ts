import { Column, Entity } from 'typeorm';
import { AbstractEntity } from '@common/base.entity';
import { AutoMap } from '@automapper/classes';

@Entity({name: 'tenant'})
export class Tenant extends AbstractEntity{
  @AutoMap()
  @Column({ name: 'name', type: 'varchar', length: 100, unique: true })
  name: string;

  @AutoMap()
  @Column({ name: 'code', type: 'varchar', length: 50, unique: true })
  code: string;

  @AutoMap()
  @Column({name: 'price_factor', type: 'decimal', precision: 10, scale: 2, default: 1.00})
  priceFactor: number;

  @AutoMap()
  @Column({name: 'currency_code', type: 'varchar', length: 3})
  currencyCode: string;

  // @AutoMap()
  // @Column({name: 'currency_symbol', type: 'varchar', length: 3})
  // currencySymbol: string;
}
