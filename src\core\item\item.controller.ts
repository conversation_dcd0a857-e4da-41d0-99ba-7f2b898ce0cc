import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { LoggerService } from '@common/logger/logger.service';
import { CoreUtils } from '@common/utils/core.utils';
import { Body, Controller, DefaultValuePipe, Delete, Get, Param, ParseIntPipe, Patch, Post, Put, Query } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { I18nService } from 'nestjs-i18n';
import { Pagination } from 'nestjs-typeorm-paginate';
import { ActivateItemDto } from './dto/activate-item.dto';
import { DeactivateItemDto } from './dto/deactivate-item.dto';
import { CreateItemDto } from './dto/create-item.dto';
import { UpdateItemDto } from './dto/update-item.dto';
import { ItemDto } from './dto/item.dto';
import { Item } from './entities/item.entity';
import { ItemService } from './item.service';

@ApiTags('Item Endpoints')
@Controller({
  path: 'items',
  version: '1',
})
export class ItemController {
  constructor(
    private readonly itemService: ItemService,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(ItemController.name);
  }

  @ApiOperation({ summary: 'Get all items' })
  @ApiQuery({ name: 'page', required: true, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: true, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'filter', required: false, type: String })
  @Get()
  async getAllItems(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('search') search: string,
    @Query('filter') filter: string,
    @CurrentRoute() route: string,
  ) {
    return CoreUtils.handleRequest(async () => {
      const { items, meta, links } = await this.itemService.findAllItems({ page, limit, search, filter }, route);
      const itemsData = await this.classMapper.mapArrayAsync(items, Item, ItemDto);
      const data = new Pagination(itemsData, meta, links);
      return { message: this.i18n.t('success.retrieved', { args: { entity: 'Items' } }), data };
    });
  }

  @ApiOperation({ summary: 'Get one item' })
  @Get(':id')
  async getOneItem(@Param('id', ParseIntPipe) id: number) {
    return CoreUtils.handleRequest(async () => {
      const item = await this.itemService.findOneItem(id);
      const data = await this.classMapper.mapAsync(item, Item, ItemDto);
      return { message: this.i18n.t('success.retrieved', { args: { entity: 'Item' } }), data };
    });
  }

  @ApiOperation({ summary: 'Create item' })
  @ApiBody({ type: CreateItemDto })
  @Post()
  async createItem(@Body() createItemDto: CreateItemDto) {
    return CoreUtils.handleRequest(async () => {
      const item = await this.itemService.createFromDto(createItemDto);
      const data = await this.classMapper.mapAsync(item, Item, ItemDto);
      return { message: this.i18n.t('success.created', { args: { entity: 'Item' } }), data };
    });
  }

  @ApiOperation({ summary: 'Update item' })
  @ApiBody({ type: UpdateItemDto })
  @Put(':id')
  async updateItem(@Param('id', ParseIntPipe) id: number, @Body() updateItemDto: UpdateItemDto) {
    return CoreUtils.handleRequest(async () => {
      const itemData = await this.classMapper.mapAsync(updateItemDto, UpdateItemDto, Item);
      itemData.id = id;
      const item = await this.itemService.modify(id, itemData);
      const data = await this.classMapper.mapAsync(item, Item, ItemDto);
      return { message: this.i18n.t('success.updated', { args: { entity: 'Item' } }), data };
    });
  }

  @ApiOperation({ summary: 'Activate items' })
  @ApiBody({ type: ActivateItemDto })
  @Patch('activate')
  async activateItems(@Body() body: ActivateItemDto) {
    return CoreUtils.handleRequest(async () => {
      await this.itemService.activate(body.ids);
      return { message: this.i18n.t('success.activated', { args: { entity: `Item${body.ids.length > 1 ? 's' : ''}` } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Deactivate items' })
  @ApiBody({ type: DeactivateItemDto })
  @Patch('deactivate')
  async deactivateItems(@Body() body: DeactivateItemDto) {
    return CoreUtils.handleRequest(async () => {
      await this.itemService.deactivate(body.ids);
      return { message: this.i18n.t('success.deactivated', { args: { entity: `Item${body.ids.length > 1 ? 's' : ''}` } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Delete item' })
  @Delete(':id')
  async deleteItem(@Param('id', ParseIntPipe) id: number) {
    return CoreUtils.handleRequest(async () => {
      await this.itemService.remove(id);
      return { message: this.i18n.t('success.deleted', { args: { entity: 'Item' } }), data: null };
    });
  }
}
