import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { LoggerService } from '@common/logger/logger.service';
import { CoreUtils } from '@common/utils/core.utils';
import { Body, Controller, DefaultValuePipe, Delete, Get, Param, ParseIntPipe, Patch, Post, Put, Query } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { I18nService } from 'nestjs-i18n';
import { Pagination } from 'nestjs-typeorm-paginate';
import { ActivateProductDto } from './dto/activate-product.dto';
import { DeactivateProductDto } from './dto/deactivate-product.dto';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductDto } from './dto/product.dto';
import { Product } from './entities/product.entity';
import { ProductService } from './product.service';

@ApiTags('Product Endpoints')
@Controller({
  path: 'products',
  version: '1',
})
export class ProductController {
  constructor(
    private readonly productService: ProductService,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(ProductController.name);
  }

  @ApiOperation({ summary: 'Get all products' })
  @ApiQuery({ name: 'page', required: true, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: true, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'filter', required: false, type: String })
  @Get()
  async getAllProducts(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('search') search: string,
    @Query('filter') filter: string,
    @CurrentRoute() route: string,
  ) {
    return CoreUtils.handleRequest(async () => {
      const { items, meta, links } = await this.productService.findAllProducts({ page, limit, search, filter }, route);
      const products = await this.classMapper.mapArrayAsync(items, Product, ProductDto);
      const data = new Pagination(products, meta, links);
      return { message: this.i18n.t('message.success.retrieved', { args: { entity: 'Products' } }), data };
    });
  }

  @ApiOperation({ summary: 'Get one product' })
  @Get(':id')
  async getOneProduct(@Param('id', ParseIntPipe) id: number) {
    return CoreUtils.handleRequest(async () => {
      const product = await this.productService.findOneProduct(id);
      const data = await this.classMapper.mapAsync(product, Product, ProductDto);
      return { message: this.i18n.t('message.success.retrieved', { args: { entity: 'Product' } }), data };
    });
  }

  @ApiOperation({ summary: 'Create product' })
  @ApiBody({ type: CreateProductDto })
  @Post()
  async createProduct(@Body() createProductDto: CreateProductDto) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.classMapper.mapAsync(createProductDto, CreateProductDto, Product);
      await this.productService.create(data);
      return { message: this.i18n.t('message.success.created', { args: { entity: 'Product' } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Update product' })
  @ApiBody({ type: UpdateProductDto })
  @Patch(':id')
  async updateProduct(@Param('id', ParseIntPipe) id: number, @Body() updateProductDto: UpdateProductDto) {
    return CoreUtils.handleRequest(async () => {
      // const productData = await this.classMapper.mapAsync(updateProductDto, UpdateProductDto, Product);
      // productData.id = id;
      const product = await this.productService.findOneProduct(id);
      await this.classMapper.mutateAsync(updateProductDto, product, UpdateProductDto, Product);
      await this.productService.modify(id, product);
      // const data = await this.classMapper.mapAsync(product, Product, ProductDto);
      return { message: this.i18n.t('message.success.updated', { args: { entity: 'Product' } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Activate products' })
  @ApiBody({ type: ActivateProductDto })
  @Patch('activate')
  async activateProducts(@Body() body: ActivateProductDto) {
    return CoreUtils.handleRequest(async () => {
      await this.productService.activate(body.ids);
      return { message: this.i18n.t('message.success.activated', { args: { entity: `Product${body.ids.length > 1 ? 's' : ''}` } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Deactivate products' })
  @ApiBody({ type: DeactivateProductDto })
  @Patch('deactivate')
  async deactivateProducts(@Body() body: DeactivateProductDto) {
    return CoreUtils.handleRequest(async () => {
      await this.productService.deactivate(body.ids);
      return { message: this.i18n.t('message.success.deactivated', { args: { entity: `Product${body.ids.length > 1 ? 's' : ''}` } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Delete product' })
  @Delete(':id')
  async deleteProduct(@Param('id', ParseIntPipe) id: number) {
    return CoreUtils.handleRequest(async () => {
      await this.productService.remove(id);
      return { message: this.i18n.t('message.success.deleted', { args: { entity: 'Product' } }), data: null };
    });
  }
}
