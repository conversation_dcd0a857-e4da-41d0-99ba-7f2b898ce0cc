import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { I18nService } from 'nestjs-i18n';
import { LoggerService } from '@common/logger/logger.service';
import { ValidationStrategy } from '@common/validation.strategy';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { Item } from './entities/item.entity';

@Injectable()
export class ItemValidationService implements ValidationStrategy<Item> {
  constructor(
    private readonly logger: LoggerService,
    @InjectRepository(Item) private readonly itemRepository: Repository<Item>,
    private readonly i18n: I18nService,
  ) {
    this.logger.setContext(ItemValidationService.name);
  }

  async validate(data: Item, action: DatabaseAction) {
    const existingItem = await this.itemRepository.findOneBy({ id: data.id });

    if (action === DatabaseAction.CREATE) {
      // Add any creation-specific validations here
    }

    if (action === DatabaseAction.UPDATE && !existingItem) {
      throw new NotFoundException(this.i18n.t('errors.not_found', { args: { entity: 'Item' } }));
    }
  }
}
